import React, { useEffect, useRef, useState, useMemo } from "react";
import CustomModal from "../../CustomModal";
import { FiSearch, FiPrinter } from "react-icons/fi";

import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { useGetProductsQuery } from "../../../store/api/menuitemApi";

interface InventorySummaryReportProps {
  isOpen: boolean;
  onClose: () => void;
}

interface InventoryItem {
  id: string;
  ingredientName: string;
  unitOfMeasurement: string;
  stockRemaining: string;
}

const InventorySummaryReport: React.FC<InventorySummaryReportProps> = ({
  isOpen,
  onClose,
}) => {

  const userId = localStorage.getItem("userId") || "";
  const { data: products, isLoading, error } = useGetProductsQuery(userId);

  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        // Handle outside click if needed
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Transform products data to inventory items
  const inventoryItems: InventoryItem[] = useMemo(() => {
    if (!products || !Array.isArray(products)) return [];

    return products.map((product: any) => {
      return {
        id: product.ProductId || product._id,
        ingredientName: product.name || "N/A",
        unitOfMeasurement: product.unit?.name || "",
        stockRemaining: `${product.totalQuantity || 0}`,
      };
    });
  }, [products]);

  // Filter inventory items based on search term (by product name)
  const filteredItems = useMemo(() => {
    if (!searchTerm.trim()) return inventoryItems;

    return inventoryItems.filter(item =>
      item.ingredientName.toLowerCase().includes(searchTerm.toLowerCase().trim())
    );
  }, [inventoryItems, searchTerm]);

  // Pagination calculations
  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
  const paginatedItems = filteredItems.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePrint = () => {
    window.print();
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const footer = (
    <div className="flex justify-between items-center p-2 no-print">
      <div className="flex items-center gap-2 border border-gray-300 rounded-2xl">
        <button
          className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={handlePreviousPage}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={handleNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next →
        </button>
      </div>
      <div className="flex gap-4 font-bold">
        <button
          onClick={onClose}
          className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handlePrint}
          className="px-14 py-2 bg-black text-white rounded-full cursor-pointer transition-colors flex items-center gap-2"
        >
          Print <FiPrinter size={20} />
        </button>
      </div>
    </div>
  );

  // Get current date for print header
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <>
      <style jsx>{`
        @media print {
          /* Hide elements that shouldn't print */
          .no-print {
            display: none !important;
          }
          
          /* Show print-only elements */
          .print-only {
            display: block !important;
          }
          
          /* Reset modal styling for print */
          .print-container {
            position: static !important;
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            background: white !important;
            box-shadow: none !important;
            margin: 0 !important;
            padding: 0 !important;
          }
          
          /* Table styling for print */
          table {
            width: 100% !important;
            border-collapse: collapse !important;
          }
          
          th {
            background-color: #f3f4f6 !important;
            font-weight: bold !important;
            text-align: left !important;
            padding: 12px 8px !important;
            border-bottom: 2px solid #e5e7eb !important;
          }
          
          td {
            padding: 12px 8px !important;
            border-bottom: 1px solid #e5e7eb !important;
          }
          
          /* Page break handling */
          @page {
            margin: 20mm;
          }
          
          /* Ensure content doesn't break across pages */
          tr {
            page-break-inside: avoid;
          }
        }
      `}</style>

      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title="Real-Time Inventory Summary Report"
        width="max-w-6xl"
        footer={footer}
      >
        <div className="p-6 print-container">
          {/* Print Header - Only visible when printing */}
          <div className="hidden print-only mb-8">
            <h1 className="text-2xl font-bold text-center mb-2">Real-Time Inventory Summary Report</h1>
            <p className="text-center text-gray-600">Generated on: {currentDate}</p>
          </div>

          {/* Search and Date Filter - Hidden when printing */}
          <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4] no-print">
            <div className="relative flex-1 mr-8">
              <div className="flex items-center">
                <FiSearch className="text-gray-400 mr-2" size={20} />
                <input
                  type="text"
                  placeholder="Search by Product Name"
                  className="w-full py-2 focus:outline-none text-base"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            {isLoading ? (
              <div className="text-center py-8 no-print">Loading inventory data...</div>
            ) : error ? (
              <div className="text-center py-8 text-red-500 no-print">
                Error loading inventory data. Please try again.
              </div>
            ) : filteredItems.length === 0 ? (
              <div className="text-center py-8 text-gray-500 no-print">
                {searchTerm ? `No products found matching "${searchTerm}"` : "No inventory items found."}
              </div>
            ) : (
              <>
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-[#9C9C9C] text-xs font-extralight border-b border-[#E4E4E4] print:text-black print:font-semibold">
                      <th className="pb-3">ID</th>
                      <th className="pb-3">Product Name</th>
                      <th className="pb-3">Unit of Measurement</th>
                      <th className="pb-3">Stock Remaining</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* For printing, show all items; for screen, show paginated items */}
                    {(typeof window !== 'undefined' && window.matchMedia('print').matches ? filteredItems : paginatedItems).map((item, index) => (
                      <tr key={`${item.id}-${index}`} className="border-b text-sm border-[#E4E4E4]">
                        <td className="py-4">{item.id}</td>
                        <td className="py-4">{item.ingredientName}</td>
                        <td className="py-4">{item.unitOfMeasurement}</td>
                        <td className="py-4">{item.stockRemaining}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {/* Pagination info - Hidden when printing */}
                <div className="mt-4 text-sm text-gray-500 text-center no-print">
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredItems.length)} of {filteredItems.length} items
                </div>

                {/* Print footer - Only visible when printing */}
                <div className="hidden print-only mt-8 pt-4 border-t border-gray-300">
                  <p className="text-sm text-gray-600 text-center">
                    Total Items: {filteredItems.length}
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      </CustomModal>
    </>
  );
};

export default InventorySummaryReport;